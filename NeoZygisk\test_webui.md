# NeoZygisk WebUI 测试指南

## 移植完成的功能

### 1. 文件结构
- ✅ 复制了完整的webroot目录结构
- ✅ 包含所有必要的CSS和JavaScript文件
- ✅ 包含Material Design UI框架

### 2. 前端修改
- ✅ 将标题从"ReZygisk WebUI"改为"NeoZygisk WebUI"
- ✅ 将路径从`/data/adb/rezygisk/module.prop`改为`/data/adb/neozygisk/module.prop`
- ✅ 更新了所有状态消息中的模块名称
- ✅ 支持中英文双语界面

### 3. 构建配置
- ✅ 修改了build.gradle.kts，确保webroot目录被包含在模块构建中
- ✅ webui文件将被正确打包到APK中

### 4. 状态更新机制
- ✅ 修改了monitor.cpp，确保状态信息写入到`/data/adb/neozygisk/module.prop`
- ✅ 在SYSTEM_SERVER_STARTED事件时复制module.prop到正确位置
- ✅ 每次状态更新时都会同步到webui可访问的位置

## 测试步骤

### 前提条件
1. 设备已安装KernelSU、APatch或其他支持webui的Root方案
2. 设备支持Zygisk功能

### 构建测试
1. 编译NeoZygisk模块：
   ```bash
   cd NeoZygisk
   ./gradlew :module:assembleRelease
   ```

2. 安装生成的模块APK

3. 重启设备以激活模块

### WebUI访问测试
1. 在KernelSU/APatch管理器中找到NeoZygisk模块
2. 点击模块进入详情页面
3. 查看是否有WebUI选项或按钮
4. 点击进入WebUI界面

### 功能验证
WebUI应该显示以下信息：
- ✅ 标题显示"NeoZygisk WebUI"
- ✅ Monitor状态（tracing/stopped/exited）
- ✅ Zygote64注入状态（injected/not injected）
- ✅ Daemon64运行状态（running/crashed）
- ✅ Zygote32注入状态（injected/not injected）
- ✅ Daemon32运行状态（running/crashed）
- ✅ Root实现信息（KernelSU/APatch/Magisk）
- ✅ 已加载的Zygisk模块列表

### 预期结果
- WebUI界面应该与ReZygisk的界面相似，但显示NeoZygisk的品牌信息
- 状态信息应该实时反映NeoZygisk的运行状态
- 支持中英文切换
- 界面应该响应式适配不同屏幕尺寸

## 故障排除

### 如果WebUI无法访问
1. 检查`/data/adb/neozygisk/module.prop`文件是否存在
2. 检查文件内容是否包含状态信息
3. 确认Root方案支持WebUI功能

### 如果状态信息不更新
1. 检查NeoZygisk monitor进程是否正在运行
2. 查看系统日志中的NeoZygisk相关信息
3. 确认模块已正确安装并激活

## 技术细节

### 数据流程
1. NeoZygisk monitor进程监控Zygote注入状态
2. 状态信息写入临时module.prop文件
3. 通过system()调用复制到`/data/adb/neozygisk/module.prop`
4. WebUI通过ksu.exec()读取该文件内容
5. JavaScript解析状态信息并更新界面

### 关键文件
- `NeoZygisk/webroot/index.html` - WebUI主界面
- `NeoZygisk/loader/src/ptracer/monitor.cpp` - 状态更新逻辑
- `NeoZygisk/module/build.gradle.kts` - 构建配置
