package org.gradle.accessors.dm;

import org.gradle.api.NonNullApi;
import org.gradle.api.artifacts.ProjectDependency;
import org.gradle.api.internal.artifacts.dependencies.ProjectDependencyInternal;
import org.gradle.api.internal.artifacts.DefaultProjectDependencyFactory;
import org.gradle.api.internal.artifacts.dsl.dependencies.ProjectFinder;
import org.gradle.api.internal.catalog.DelegatingProjectDependency;
import org.gradle.api.internal.catalog.TypeSafeProjectDependencyFactory;
import javax.inject.Inject;

@NonNullApi
public class RootProjectAccessor extends TypeSafeProjectDependencyFactory {


    @Inject
    public RootProjectAccessor(DefaultProjectDependencyFactory factory, ProjectFinder finder) {
        super(factory, finder);
    }

    /**
     * Creates a project dependency on the project at path ":"
     */
    public ReZygiskProjectDependency getReZygisk() { return new ReZygiskProjectDependency(getFactory(), create(":")); }

    /**
     * Creates a project dependency on the project at path ":loader"
     */
    public LoaderProjectDependency getLoader() { return new LoaderProjectDependency(getFactory(), create(":loader")); }

    /**
     * Creates a project dependency on the project at path ":module"
     */
    public ModuleProjectDependency getModule() { return new ModuleProjectDependency(getFactory(), create(":module")); }

    /**
     * Creates a project dependency on the project at path ":zygiskd"
     */
    public ZygiskdProjectDependency getZygiskd() { return new ZygiskdProjectDependency(getFactory(), create(":zygiskd")); }

}
